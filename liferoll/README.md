# Liferoll

A web-based life simulation board game built with PHP.

## Project Structure

- `assets/` - Static files (CSS, images, icons)
- `includes/` - Reusable PHP components
- `screens/` - Main game pages
- `tests/` - Testing documentation

## Getting Started

(Add setup instructions here)

## Features

- Multiple life stages (early-life, mid-life, pre-retirement)
- Dice-based gameplay
- Leaderboard system
- Win/lose conditions

## Requirements

- PHP web server
- Modern web browser
