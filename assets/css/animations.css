/* Animations for Liferoll Board Game */

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
}

/* <PERSON><PERSON> Roll Animation */
@keyframes roll {
    0% {
        transform: rotate(0deg) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: rotate(360deg) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: rotate(720deg) scale(1);
        opacity: 1;
    }
}

.dice {
    animation: roll 1s ease-out;
}

.dice-rolling {
    animation: spin 0.5s ease-in-out infinite;
}

/* Cloud Transition Between Boards */
.cloud-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 248, 255, 0.9) 100%);
    animation: cloud-in 0.5s forwards, cloud-out 0.5s 1s forwards;
    z-index: 1000;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
}

@keyframes cloud-in {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes cloud-out {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(1.5);
    }
}

/* Animation Classes */
.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-slideIn {
    animation: slideIn 0.5s ease-out;
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-in;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-glow {
    animation: glow 2s infinite;
}
