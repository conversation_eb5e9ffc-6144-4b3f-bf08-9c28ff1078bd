/* <PERSON><PERSON> Roll Animation */
@keyframes roll {
  0% { transform: rotate(0deg) scale(0.5); opacity: 0; }
  100% { transform: rotate(720deg) scale(1); opacity: 1; }
}

.dice {
  animation: roll 1s ease-out;
}

/* Cloud Transition */
@keyframes cloud-in {
  0% { opacity: 0; transform: scale(0.5); }
  100% { opacity: 1; transform: scale(1); }
}
/* Cloud Transition Between Boards */
.cloud-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../images/cloud.png') center/cover no-repeat;
  animation: cloud-in 1s forwards, cloud-out 1s 1s forwards;
  z-index: 1000;
  pointer-events: none;
}

@keyframes cloud-in {
  0% { opacity: 0; transform: scale(0.5); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes cloud-out {
  0% { opacity: 1; transform: scale(1); }
  100% { opacity: 0; transform: scale(1.5); }
}
