/* Global styles for Liferoll Board Game */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Navigation */
.game-nav {
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a5568;
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-links a {
    text-decoration: none;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-links a:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Main Menu Styles */
.main-menu {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
    backdrop-filter: blur(10px);
}

.main-menu h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #2d3748;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.game-logo {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
}

.dice-icon, .money-icon {
    font-size: 4rem;
    animation: bounce 2s infinite;
}

.money-icon {
    animation-delay: 0.5s;
}

.rules-box {
    background: #f7fafc;
    border: 3px solid #e2e8f0;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
}

.rules-box h2 {
    color: #2d3748;
    margin-bottom: 1rem;
    text-align: center;
}

.rules-box ul {
    list-style: none;
    padding: 0;
}

.rules-box li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
    border-bottom: 1px solid #e2e8f0;
}

.rules-box li:last-child {
    border-bottom: none;
}

/* Button Styles */
.menu-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 12px;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
    min-width: 150px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #2d3748;
    box-shadow: 0 4px 15px rgba(252, 182, 159, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(252, 182, 159, 0.6);
}

/* Footer */
.game-footer {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-menu {
        margin: 1rem;
        padding: 1rem;
    }

    .main-menu h1 {
        font-size: 2rem;
    }

    .menu-buttons {
        flex-direction: column;
        align-items: center;
    }

    .game-nav {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
}
