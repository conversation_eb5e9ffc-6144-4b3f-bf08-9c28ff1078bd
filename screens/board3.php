<?php
session_start();
include '../includes/header.php';

// Display starting wealth info
$starting_wealth = $_SESSION['starting_wealth'] ?? $_SESSION['wealth'];
?>

<div class="board-container animate-slideIn">
    <div class="board-header">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; border-radius: 15px; margin-bottom: 1rem; text-align: center;">
            <h3>🎯 You started with: $<?php echo number_format($starting_wealth); ?></h3>
        </div>

        <h1 class="board-title">🏖️ Pre-Retirement Board (Ages 50-65)</h1>
        <div class="player-stats">
            <div class="stat-item">💰 Current Wealth: $<?php echo number_format($_SESSION['wealth']); ?></div>
            <div class="stat-item">🎂 Age: <?php echo $_SESSION['age']; ?></div>
            <div class="stat-item">📍 Position: <?php echo $_SESSION['position']; ?>/20</div>
        </div>

        <?php if (isset($_SESSION['last_choice'])): ?>
            <div style="background: #e6fffa; border: 2px solid #38b2ac; border-radius: 10px; padding: 1rem; margin: 1rem 0; text-align: center;">
                <strong>Last Action:</strong> <?php echo $_SESSION['last_choice']; unset($_SESSION['last_choice']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['last_event'])): ?>
            <div style="background: #fff5f5; border: 2px solid #fc8181; border-radius: 10px; padding: 1rem; margin: 1rem 0; text-align: center;">
                <strong>Dice Event:</strong> <?php echo $_SESSION['last_event']; unset($_SESSION['last_event']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['last_roll'])): ?>
            <div style="background: #f0fff4; border: 2px solid #48bb78; border-radius: 10px; padding: 1rem; margin: 1rem 0; text-align: center;">
                <strong>🎲 Last Roll:</strong> You rolled a <?php echo $_SESSION['last_roll']; ?>! <?php unset($_SESSION['last_roll']); ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Game Board Path -->
    <div class="game-board">
        <h3 style="text-align: center; margin-bottom: 1rem;">🎯 Road to Retirement</h3>
        <div class="board-path">
            <?php for ($i = 1; $i <= 20; $i++): ?>
                <div class="board-space <?php echo ($_SESSION['position'] == $i) ? 'current-position' : ''; ?>">
                    <div class="space-number"><?php echo $i; ?></div>
                    <div class="space-event">
                        <?php
                        $events = [
                            1 => "🏖️ Planning", 2 => "💰 Savings", 3 => "📊 Portfolio", 4 => "🏠 Downsize", 5 => "👨‍👩‍👧‍👦 Legacy",
                            6 => "📈 Invest", 7 => "🎯 Goals", 8 => "💡 Wisdom", 9 => "🤝 Mentor", 10 => "🌟 Peak",
                            11 => "💰 Wealth", 12 => "🏆 Success", 13 => "📚 Knowledge", 14 => "🎨 Hobbies", 15 => "🌍 Travel",
                            16 => "👴 Elder", 17 => "🎯 Final", 18 => "🏖️ Ready", 19 => "🎊 Almost", 20 => "🏁 Finish"
                        ];
                        echo $events[$i];
                        ?>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
    </div>

    <!-- Current Space Event -->
    <?php
    $current_space = $_SESSION['position'];
    $space_events = [
        1 => ['name' => '🏖️ Retirement Planning', 'effect' => '+$2,000 from planning'],
        2 => ['name' => '💰 Savings Milestone', 'effect' => '+$5,000 achievement'],
        3 => ['name' => '📊 Portfolio Review', 'effect' => '+$3,000 optimization'],
        4 => ['name' => '🏠 Downsize Home', 'effect' => '+$1,000 from smaller place'],
        5 => ['name' => '👨‍👩‍👧‍👦 Legacy Planning', 'effect' => '+$4,000 estate planning'],
        6 => ['name' => '📈 Final Investment', 'effect' => 'Big risk, big reward!'],
        7 => ['name' => '🎯 Financial Goals', 'effect' => '+$2,500 target met'],
        8 => ['name' => '💡 Wisdom Pays', 'effect' => '+$3,500 experience bonus'],
        9 => ['name' => '🤝 Mentoring', 'effect' => '+$4,500 teaching income'],
        10 => ['name' => '🌟 Peak Earnings', 'effect' => '+$6,000 career high'],
        11 => ['name' => '💰 Wealth Growth', 'effect' => '+$5,500 compound interest'],
        12 => ['name' => '🏆 Life Achievement', 'effect' => '+$7,000 recognition'],
        13 => ['name' => '📚 Share Knowledge', 'effect' => '+$3,000 consulting'],
        14 => ['name' => '🎨 Retirement Hobbies', 'effect' => '+$2,000 passion projects'],
        15 => ['name' => '🌍 Dream Vacation', 'effect' => '-$2,000 bucket list'],
        16 => ['name' => '👴 Elder Wisdom', 'effect' => '+$4,000 advisory role'],
        17 => ['name' => '🎯 Final Push', 'effect' => '+$5,000 last effort'],
        18 => ['name' => '🏖️ Almost Ready', 'effect' => '+$6,000 preparation'],
        19 => ['name' => '🎊 So Close', 'effect' => '+$8,000 anticipation'],
        20 => ['name' => '🏁 Retirement Ready', 'effect' => '+$10,000 completion bonus']
    ];

    if (isset($space_events[$current_space])):
    ?>
    <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 15px; padding: 2rem; margin: 2rem 0; text-align: center;">
        <h3>📍 Current Space: <?php echo $space_events[$current_space]['name']; ?></h3>
        <p style="font-size: 1.2rem; margin: 1rem 0;"><?php echo $space_events[$current_space]['effect']; ?></p>
        <form method="post" action="process_space_event.php" style="display: inline;">
            <input type="hidden" name="board" value="3">
            <input type="hidden" name="space" value="<?php echo $current_space; ?>">
            <button type="submit" class="btn btn-primary">✅ Apply Event</button>
        </form>
    </div>
    <?php endif; ?>

    <!-- Dice Rolling Section -->
    <div class="dice-section">
        <h3>🎲 Final Steps to Retirement!</h3>
        <div class="dice-display" id="diceDisplay">🎲</div>
        <form method="post" action="roll.php" style="display: inline;">
            <input type="hidden" name="board" value="3">
            <button type="submit" class="btn btn-roll">🎲 Roll Dice & Move</button>
        </form>
        <p style="margin-top: 1rem; color: #666;">Each roll moves you closer to retirement!</p>
    </div>

    <!-- Navigation -->
    <div class="action-buttons">
        <a href="board2.php" class="btn btn-secondary">⬅️ Back to Mid-Life</a>
        <a href="index.php" class="btn btn-secondary">🏠 Main Menu</a>
        <?php if ($_SESSION['position'] >= 20): ?>
            <a href="win.php" class="btn btn-primary">🏆 Retire & See Results!</a>
        <?php endif; ?>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
