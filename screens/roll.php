<?php
session_start();

// Roll dice (1-6)
$roll = rand(1, 6);
$_SESSION['last_roll'] = $roll;

// Move player
$_SESSION['position'] += $roll;
$_SESSION['age'] += 1; // Age 1 year per move

// Smaller random events based on dice roll (less impactful)
$random_events = [
    1 => ['message' => '😔 Minor setback! -$200', 'wealth' => -200],
    2 => ['message' => '💰 Found some cash! +$150', 'wealth' => 150],
    3 => ['message' => '🎯 Steady progress', 'wealth' => 0],
    4 => ['message' => '🎯 Normal day', 'wealth' => 0],
    5 => ['message' => '🎉 Small win! +$100', 'wealth' => 100],
    6 => ['message' => '🚀 Great roll! +$250', 'wealth' => 250]
];

$event = $random_events[$roll];
$_SESSION['wealth'] += $event['wealth'];
$_SESSION['last_event'] = $event['message'];

// Check for game over (bankruptcy)
if ($_SESSION['wealth'] <= 0) {
    header('Location: gameover.php');
    exit;
}

// Check if completed board
$current_board = $_SESSION['board'] ?? 1;
if ($_SESSION['position'] >= 20) {
    if ($current_board == 1) {
        $_SESSION['board'] = 2;
        $_SESSION['position'] = 1;
        $_SESSION['last_event'] = '🎊 Completed Early Life! Moving to Mid-Life Board!';
        header('Location: board2.php');
        exit;
    } elseif ($current_board == 2) {
        $_SESSION['board'] = 3;
        $_SESSION['position'] = 1;
        $_SESSION['last_event'] = '🎊 Completed Mid-Life! Moving to Pre-Retirement Board!';
        header('Location: board3.php');
        exit;
    } elseif ($current_board == 3) {
        $_SESSION['last_event'] = '🏆 Completed all boards! Time to retire!';
        header('Location: win.php');
        exit;
    }
}

// Return to current board
header("Location: board{$current_board}.php");
exit;
?>
