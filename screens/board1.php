<?php
session_start();

// Initialize game if not started
if (!isset($_SESSION['wealth'])) {
    $_SESSION['wealth'] = rand(5000, 15000);
    $_SESSION['starting_wealth'] = $_SESSION['wealth'];
    $_SESSION['age'] = 18;
    $_SESSION['position'] = 1;
    $_SESSION['board'] = 1;
    $_SESSION['career_boost'] = 0;
    $_SESSION['experience'] = 0;
    $_SESSION['education'] = 'none';
}

include '../includes/header.php';
?>

<div class="board-container animate-slideIn">
    <div class="board-header">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; border-radius: 15px; margin-bottom: 1rem; text-align: center;">
            <h3>🎯 You started with: $<?php echo number_format($_SESSION['starting_wealth']); ?></h3>
        </div>

        <h1 class="board-title">🌱 Early Life Board (Ages 18-30)</h1>
        <div class="player-stats">
            <div class="stat-item">💰 Current Wealth: $<?php echo number_format($_SESSION['wealth']); ?></div>
            <div class="stat-item">🎂 Age: <?php echo $_SESSION['age']; ?></div>
            <div class="stat-item">📍 Position: <?php echo $_SESSION['position']; ?>/20</div>
        </div>

        <?php if (isset($_SESSION['last_choice'])): ?>
            <div style="background: #e6fffa; border: 2px solid #38b2ac; border-radius: 10px; padding: 1rem; margin: 1rem 0; text-align: center;">
                <strong>Last Action:</strong> <?php echo $_SESSION['last_choice']; unset($_SESSION['last_choice']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['last_event'])): ?>
            <div style="background: #fff5f5; border: 2px solid #fc8181; border-radius: 10px; padding: 1rem; margin: 1rem 0; text-align: center;">
                <strong>Dice Event:</strong> <?php echo $_SESSION['last_event']; unset($_SESSION['last_event']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['last_roll'])): ?>
            <div style="background: #f0fff4; border: 2px solid #48bb78; border-radius: 10px; padding: 1rem; margin: 1rem 0; text-align: center;">
                <strong>🎲 Last Roll:</strong> You rolled a <?php echo $_SESSION['last_roll']; ?>! <?php unset($_SESSION['last_roll']); ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Game Board Path -->
    <div class="game-board">
        <h3 style="text-align: center; margin-bottom: 1rem;">🎯 Life Path</h3>
        <div class="board-path">
            <?php for ($i = 1; $i <= 20; $i++): ?>
                <div class="board-space <?php echo ($_SESSION['position'] == $i) ? 'current-position' : ''; ?>">
                    <div class="space-number"><?php echo $i; ?></div>
                    <div class="space-event">
                        <?php
                        $events = [
                            1 => "🏠 Start", 2 => "📚 Study", 3 => "💼 Job Fair", 4 => "🎉 Party", 5 => "💰 Savings",
                            6 => "🚗 Car", 7 => "🏠 Apartment", 8 => "📱 Tech", 9 => "🎓 Degree", 10 => "💕 Love",
                            11 => "🌍 Travel", 12 => "💼 Career", 13 => "🏋️ Gym", 14 => "🎨 Hobby", 15 => "💰 Bonus",
                            16 => "🏠 Move", 17 => "📈 Invest", 18 => "🎯 Goal", 19 => "🚀 Launch", 20 => "🎊 Success"
                        ];
                        echo $events[$i];
                        ?>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
    </div>

    <!-- Current Space Event -->
    <?php
    $current_space = $_SESSION['position'];
    $space_events = [
        1 => ['name' => '🏠 Starting Journey', 'effect' => 'Welcome to adulthood!'],
        2 => ['name' => '📚 Study Hard', 'effect' => '+$500 from good grades'],
        3 => ['name' => '💼 Job Fair', 'effect' => '+$1,000 part-time work'],
        4 => ['name' => '🎉 College Party', 'effect' => '-$500 fun expenses'],
        5 => ['name' => '💰 Save Money', 'effect' => '+$800 from budgeting'],
        6 => ['name' => '🚗 First Car', 'effect' => '-$1,200 transportation'],
        7 => ['name' => '🏠 Move Out', 'effect' => '-$2,000 apartment setup'],
        8 => ['name' => '📱 New Tech', 'effect' => '-$800 gadgets'],
        9 => ['name' => '🎓 Graduate', 'effect' => '+$2,000 achievement bonus'],
        10 => ['name' => '💕 Dating', 'effect' => '-$1,000 relationship costs'],
        11 => ['name' => '🌍 Travel', 'effect' => '-$1,500 adventure'],
        12 => ['name' => '💼 Great Job', 'effect' => '+$3,000 career start'],
        13 => ['name' => '🏋️ Fitness', 'effect' => '-$300 health investment'],
        14 => ['name' => '🎨 Side Hustle', 'effect' => '+$600 creative income'],
        15 => ['name' => '💰 Bonus', 'effect' => '+$1,500 performance reward'],
        16 => ['name' => '🏠 Moving', 'effect' => '-$1,800 relocation costs'],
        17 => ['name' => '📈 Investment', 'effect' => '+$2,200 smart choice'],
        18 => ['name' => '🎯 Goal Achieved', 'effect' => '+$1,000 milestone'],
        19 => ['name' => '🚀 Career Launch', 'effect' => '+$2,500 opportunity'],
        20 => ['name' => '🎊 Early Success', 'effect' => '+$3,000 achievement']
    ];

    if (isset($space_events[$current_space])):
    ?>
    <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border-radius: 15px; padding: 2rem; margin: 2rem 0; text-align: center;">
        <h3>📍 Current Space: <?php echo $space_events[$current_space]['name']; ?></h3>
        <p style="font-size: 1.2rem; margin: 1rem 0;"><?php echo $space_events[$current_space]['effect']; ?></p>
        <form method="post" action="process_space_event.php" style="display: inline;">
            <input type="hidden" name="board" value="1">
            <input type="hidden" name="space" value="<?php echo $current_space; ?>">
            <button type="submit" class="btn btn-primary">✅ Apply Event</button>
        </form>
    </div>
    <?php endif; ?>

    <!-- Dice Rolling Section -->
    <div class="dice-section">
        <h3>🎲 Roll the Dice to Move Forward!</h3>
        <div class="dice-display" id="diceDisplay">🎲</div>
        <form method="post" action="roll.php" style="display: inline;">
            <input type="hidden" name="board" value="1">
            <button type="submit" class="btn btn-roll">🎲 Roll Dice & Move</button>
        </form>
        <p style="margin-top: 1rem; color: #666;">Each roll moves you forward and triggers events based on where you land!</p>
    </div>

    <!-- Navigation -->
    <div class="action-buttons">
        <a href="index.php" class="btn btn-secondary">🏠 Back to Menu</a>
        <?php if ($_SESSION['position'] >= 20): ?>
            <a href="board2.php" class="btn btn-primary">➡️ Next: Mid-Life Board</a>
        <?php endif; ?>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
